.auth-form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
  background-color: #f9f9f9;
}

.auth-form {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  width: 100%;
  max-width: 500px;
  transition: all 0.3s ease;
}

.auth-form:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.auth-form h2 {
  color: #2e7d32;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #2e7d32;
  box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2);
}

.auth-button {
  width: 100%;
  background-color: #2e7d32;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  display: block;
  text-decoration: none;
}

.auth-button:hover {
  background-color: #1b5e20;
  transform: translateY(-2px);
}

.auth-button:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 0.75rem;
  border-radius: 5px;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.auth-redirect {
  text-align: center;
  margin-top: 1.5rem;
  color: #666;
}

.auth-link {
  color: #2e7d32;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.auth-link:hover {
  color: #1b5e20;
  text-decoration: underline;
}

/* Location specific styles */
.location-group {
  margin-bottom: 2rem;
}

.location-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.location-button {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #a5d6a7;
  border-radius: 5px;
  padding: 0.6rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.location-button:hover {
  background-color: #c8e6c9;
}

.location-status {
  font-size: 0.9rem;
  color: #666;
}

.location-status.success {
  color: #2e7d32;
}

.location-status span {
  font-weight: bold;
}

/* Success message styles */
.success-message {
  text-align: center;
  padding: 1rem;
}

.success-icon {
  background-color: #2e7d32;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
}

.success-message h3 {
  color: #2e7d32;
  margin-bottom: 1rem;
}

.success-message p {
  color: #666;
  margin-bottom: 2rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .auth-form {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .auth-form-container {
    padding: 1rem;
  }
  
  .auth-form {
    padding: 1.5rem;
  }
  
  .auth-form h2 {
    font-size: 1.75rem;
  }
}
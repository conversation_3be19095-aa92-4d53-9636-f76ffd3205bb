import {
  findUserByEmail,
  findUserByEmailandPassword,
  createUser,
} from "../DAO/user.dao.js";
import { signToken } from "../utils/helper.js";

export const registerUser = async (name, email, password ,role) => {
  // check if user already exists
  const user = await findUserByEmail(email);
  if (user) {
    throw new Error("User already Exist");
  }
  //   create user if not exists
  const newUser = await createUser(name, email, password,role);
  //    sign token payload is id of user
  const token = signToken({ id: newUser._id });
  return { newUser, token };
};

export const loginUser = async (email, password) => {
  const user = await findUserByEmailandPassword(email);
  // check if user exists
  if (!user) {
    throw new Error("User does not exists");
  } 
  if (user.password !== password) {
    throw new Error("Invalid password");
  }
  // sign token
  const token = signToken({ id: user._id });
  return { user, token };
};


// what is signToken ??
// signToken is a function which is defined in utils folder  it create a token which furthers stored in cookie and sign token takes payload(id) , secret key , and options
// It is necessar to sign on registering as well as on login because we need to create a token for user to access protected routes
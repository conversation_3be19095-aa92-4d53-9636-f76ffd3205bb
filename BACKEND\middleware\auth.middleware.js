// middleware/auth.middleware.js
import jwt from 'jsonwebtoken';
import User from '../models/user.model.js';

export const isAuthenticated = async (req, res, next) =>{
  // takes the token from cookie
  const token = req.cookies.AccessToken;
  console.log(token)
  // if token not found unauthorized
  if (!token) {
    return res.status(401).json({ message: 'Unauthorized: No token provided' });
  }
  // verify token
  try {
    // decoded is an object which contains id of user returns id of user
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = await User.findById(decoded.id).select('-password');
    console.log(req.user)
    next();
  } catch (error) {
    res.status(401).json({ message: 'Invalid or expired token' });
  }
};


// jwt.verify(token, process.env.JWT_SECRET) is used to verify the token and it returns an object which contains id of user

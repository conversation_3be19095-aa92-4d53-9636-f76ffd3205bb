.dashboard-container {
  min-height: calc(100vh - 150px);
  background-color: #f9f9f9;
  padding: 3rem 1rem;
  scroll-behavior: smooth;
}

/* Dashboard Navigation Pills */
.dashboard-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  position: sticky;
  top: 80px;
  z-index: 100;
  background: rgba(249, 249, 249, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 0;
  border-radius: 50px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 3rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.nav-pill {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.nav-pill:hover {
  background: #f8f9fa;
  border-color: #3CC78F;
  color: #3CC78F;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(60, 199, 143, 0.2);
}

.nav-pill.active {
  background: linear-gradient(135deg, #3CC78F, #2e7d32);
  border-color: #3CC78F;
  color: white;
  box-shadow: 0 4px 15px rgba(60, 199, 143, 0.3);
}

.nav-pill.active:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(60, 199, 143, 0.4);
}

/* Scroll Section Styling */
.scroll-section {
  scroll-margin-top: 120px;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.donation-form-container {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  border-radius: 15px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(60, 199, 143, 0.1);
}

.donation-form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3CC78F, #2e7d32);
}

.donation-form-container:hover {
  box-shadow: 0 12px 35px rgba(60, 199, 143, 0.15);
  transform: translateY(-2px);
}

.donation-form-container h2 {
  color: #2e7d32;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: 600;
  position: relative;
  padding-bottom: 0.5rem;
}

.donation-form-container h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #2e7d32;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fafafa;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3CC78F;
  box-shadow: 0 0 0 3px rgba(60, 199, 143, 0.1);
  background: white;
  transform: translateY(-1px);
}

.form-group input:hover,
.form-group select:hover {
  border-color: #3CC78F;
  background: white;
}

.form-group small {
  display: block;
  margin-top: 0.5rem;
  color: #666;
  font-size: 0.85rem;
}

.location-input-group {
  display: flex;
  gap: 0.5rem;
}

.location-input-group input {
  flex: 1;
}

.location-button {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #a5d6a7;
  border-radius: 5px;
  padding: 0 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.location-button:hover {
  background-color: #c8e6c9;
}

.submit-button {
  width: 100%;
  background: linear-gradient(135deg, #3CC78F, #2e7d32);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1.5rem;
  box-shadow: 0 4px 15px rgba(60, 199, 143, 0.3);
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover {
  background: linear-gradient(135deg, #2e7d32, #1b5e20);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(60, 199, 143, 0.4);
}

.submit-button:active {
  transform: translateY(-1px);
}

.submit-button:disabled {
  background: #a5d6a7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-button:disabled::before {
  display: none;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 0.75rem;
  border-radius: 5px;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

/* Success message styles */
.success-message {
  text-align: center;
  padding: 2rem 1rem;
}

.success-icon {
  background-color: #2e7d32;
  color: white;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  margin: 0 auto 1.5rem;
}

.success-message h3 {
  color: #2e7d32;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.success-message p {
  color: #666;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.button-group {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primary-button,
.secondary-button {
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primary-button {
  background-color: #2e7d32;
  color: white;
  border: none;
}

.primary-button:hover {
  background-color: #1b5e20;
  transform: translateY(-2px);
}

.secondary-button {
  background-color: white;
  color: #2e7d32;
  border: 1px solid #2e7d32;
}

.secondary-button:hover {
  background-color: #f1f8e9;
  transform: translateY(-2px);
}

/* Responsive styles */
@media (max-width: 768px) {
  .dashboard-nav {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    margin-bottom: 2rem;
  }

  .nav-pill {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .donation-form-container {
    padding: 2rem;
  }

  .location-input-group {
    flex-direction: column;
  }

  .button-group {
    flex-direction: column;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
  }

  .my-donations-container {
    max-height: 60vh;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 2rem 1rem;
  }
  
  .donation-form-container {
    padding: 1.5rem;
  }
  
  .donation-form-container h2 {
    font-size: 1.75rem;
  }
}

/* Add these styles to your existing Dashboard.css file */

.dashboard-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

@media (min-width: 992px) {
  .dashboard-layout {
    grid-template-columns: 1fr 1fr;
  }
}

.my-donations-container {
  background-color: white;
  border-radius: 15px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(60, 199, 143, 0.1);
  max-height: 80vh;
  overflow-y: auto;
}

.my-donations-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3CC78F, #2e7d32);
}

.my-donations-container:hover {
  box-shadow: 0 12px 35px rgba(60, 199, 143, 0.15);
  transform: translateY(-2px);
}

/* Custom Scrollbar for My Donations */
.my-donations-container::-webkit-scrollbar {
  width: 8px;
}

.my-donations-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.my-donations-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3CC78F, #2e7d32);
  border-radius: 10px;
}

.my-donations-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2e7d32, #1b5e20);
}

.my-donations-container h2 {
  color: #2e7d32;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: 600;
  position: relative;
  padding-bottom: 0.5rem;
}

.my-donations-container h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #2e7d32;
}

.donations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.donation-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background-color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid rgba(60, 199, 143, 0.1);
  animation: slideInCard 0.6s ease-out;
}

.donation-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 8px 25px rgba(60, 199, 143, 0.2);
  border-color: rgba(60, 199, 143, 0.3);
}

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.donation-image {
  height: 180px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.donation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.donation-card:hover .donation-image img {
  transform: scale(1.05);
}

.donation-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-style: italic;
}

.donation-content {
  padding: 1.25rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.donation-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.donation-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: auto;
}

.donation-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.95rem;
}

.detail-label {
  font-weight: 500;
  color: #666;
}

.detail-value {
  color: #333;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.85rem;
  font-weight: 500;
  text-align: center;
}

.status-available {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-claimed {
  background-color: #fff8e1;
  color: #f57f17;
}

.status-picked {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-expired {
  background-color: #ffebee;
  color: #c62828;
}

.loading-spinner {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

.no-donations {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
}

.no-donations p {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .donations-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
  
  .donation-image {
    height: 160px;
  }
}

@media (max-width: 480px) {
  .donations-grid {
    grid-template-columns: 1fr;
  }
  
  .my-donations-container {
    padding: 1.5rem;
  }
  
  .my-donations-container h2 {
    font-size: 1.75rem;
  }
}
/* request tab styling */


.request-tab-container {
  position: relative;
  z-index: 1100;
}

.request-tab-button {
  background-color: #3CC78F;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.request-tab-button:hover {
  background-color: #2e7d32;
  transform: scale(1.05);
}

.request-icon {
  font-size: 1.2rem;
}

.request-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
}

.request-tab-panel {
  position: absolute;
  top: 50px;
  right: 0;
  width: 320px;
  max-height: 450px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transform: translateY(-20px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.request-tab-panel.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.request-tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #3CC78F;
  color: white;
}

.request-tab-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.request-tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.request-list {
  flex: 1;
  overflow-y: auto;
  max-height: 350px;
}

.request-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.request-item:hover {
  background-color: #f9f9f9;
}

.request-item.unread {
  background-color: #e8f5e9;
}

.request-item.unread:hover {
  background-color: #d7edd8;
}

.request-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 5px;
}

.request-item-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.request-time {
  font-size: 0.75rem;
  color: #777;
  white-space: nowrap;
}

.request-from {
  margin: 5px 0;
  font-size: 0.9rem;
  color: #555;
}

.request-status {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-indicator.pending {
  background-color: #FFC107;
}

.status-indicator.accepted {
  background-color: #4CAF50;
}

.status-indicator.rejected {
  background-color: #F44336;
}

.status-text {
  font-size: 0.8rem;
  color: #666;
}

.request-actions {
  display: flex;
  gap: 8px;
}

.accept-button,
.reject-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.accept-button {
  background-color: #3CC78F;
  color: white;
}

.accept-button:hover {
  background-color: #2e7d32;
}

.reject-button {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.reject-button:hover {
  background-color: #e0e0e0;
}

.view-all-button {
  margin: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.view-all-button:hover {
  background-color: #e0e0e0;
}

.request-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: #666;
}

.request-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3CC78F;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.no-requests {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
  text-align: center;
}

.no-requests-icon {
  font-size: 2rem;
  margin-bottom: 10px;
  opacity: 0.7;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive styles */
@media (max-width: 480px) {
  .request-tab-panel {
    width: 100%;
    max-width: 100%;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    max-height: 100vh;
  }
  
  .request-list {
    max-height: calc(100vh - 130px);
  }
}
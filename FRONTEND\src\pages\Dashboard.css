/* Dashboard Container with Modern Background */
.dashboard-container {
  min-height: calc(100vh - 150px);
  background: linear-gradient(135deg, #f8fffe 0%, #f0f9f7 50%, #e8f5f0 100%);
  padding: 2rem 1rem;
  scroll-behavior: smooth;
  position: relative;
  overflow-x: hidden;
}

.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(60,199,143,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
}

/* Dashboard Navigation Pills */
.dashboard-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  position: sticky;
  top: 20px;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 1.25rem 2rem;
  border-radius: 60px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 3rem;
  box-shadow: 0 8px 32px rgba(60, 199, 143, 0.15);
  border: 1px solid rgba(60, 199, 143, 0.1);
}

.nav-pill {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2px solid transparent;
  border-radius: 30px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.nav-pill::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(60, 199, 143, 0.1), transparent);
  transition: left 0.6s ease;
}

.nav-pill:hover::before {
  left: 100%;
}

.nav-pill:hover {
  background: linear-gradient(135deg, #f0f9f7 0%, #e8f5f0 100%);
  border-color: #3CC78F;
  color: #2e7d32;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(60, 199, 143, 0.25);
}

.nav-pill.active {
  background: linear-gradient(135deg, #3CC78F 0%, #2e7d32 100%);
  border-color: #3CC78F;
  color: white;
  box-shadow: 0 8px 25px rgba(60, 199, 143, 0.4);
  transform: translateY(-2px);
}

.nav-pill.active:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 30px rgba(60, 199, 143, 0.5);
}

/* Scroll Section Styling */
.scroll-section {
  scroll-margin-top: 140px;
  animation: slideInSection 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInSection {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dashboard Layout */
.dashboard-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

@media (min-width: 1200px) {
  .dashboard-layout {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

/* Modern Form Container with Glassmorphism */
.donation-form-container {
  max-width: 900px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(60, 199, 143, 0.15),
    0 1px 0 #ffffffcc inset;
  padding: 3rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(60, 199, 143, 0.2);
  height:80vh;
}

.donation-form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3CC78F 0%, #2e7d32 50%, #3CC78F 100%);
  background-size: 200% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.donation-form-container::after {
  content: '';
  position: absolute;
  top: 6px;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(60, 199, 143, 0.02) 0%, transparent 100%);
  pointer-events: none;
}

.donation-form-container:hover {
  box-shadow:
    0 16px 48px rgba(60, 199, 143, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.9) inset;
  transform: translateY(-4px) scale(1.01);
  border-color: rgba(60, 199, 143, 0.3);
}

/* Enhanced Form Title */
.donation-form-container h2 {
  background: linear-gradient(135deg, #2e7d32 0%, #3CC78F 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  margin-bottom: 2.5rem;
  font-size: 2.5rem;
  font-weight: 700;
  position: relative;
  padding-bottom: 1rem;
  letter-spacing: -0.5px;
}

.donation-form-container h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, #3CC78F, #2e7d32, #3CC78F);
  border-radius: 2px;
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 1;
    transform: translateX(-50%) scaleX(1);
  }
  50% {
    opacity: 0.7;
    transform: translateX(-50%) scaleX(1.1);
  }
}

/* Enhanced Form Groups */
.form-group {
  margin-bottom: 2rem;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #2e7d32;
  font-size: 0.8rem;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 0.9rem;
}

/* Modern Input Styling */
.form-group input,
.form-group select {
  width: 100%;
  padding: 1.rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  font-size: 0.8rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  font-family: inherit;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3CC78F;
  box-shadow:
    0 0 0 4px rgba(60, 199, 143, 0.15),
    0 4px 20px rgba(60, 199, 143, 0.1);
  background: white;
  transform: translateY(-2px) scale(1.01);
}

.form-group input:hover,
.form-group select:hover {
  border-color: #3CC78F;
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(60, 199, 143, 0.1);
}

/* Input Focus Animation */
.form-group input:focus + .input-highlight,
.form-group select:focus + .input-highlight {
  transform: scaleX(1);
}

.input-highlight {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  width: 100%;
  background: linear-gradient(90deg, #3CC78F, #2e7d32);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 1px;
}

/* Form Helper Text */
.form-group small {
  display: block;
  margin-top: 0.75rem;
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
  opacity: 0.8;
}

/* Enhanced Location Input Group */
.location-input-group {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.location-input-group input {
  flex: 1;
}

.location-button {
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
  color: #2e7d32;
  border: 2px solid #a5d6a7;
  border-radius: 12px;
  padding: 1.25rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  box-shadow: 0 2px 10px rgba(60, 199, 143, 0.1);
  position: relative;
  overflow: hidden;
}

.location-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.location-button:hover::before {
  left: 100%;
}

.location-button:hover {
  background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 20px rgba(60, 199, 143, 0.2);
  border-color: #3CC78F;
}

/* Modern Submit Button */
.submit-button {
  width: 100%;
  background: linear-gradient(135deg, #3CC78F 0%, #2e7d32 100%);
  color: white;
  border: none;
  border-radius: 16px;
  padding: 1.25rem 2rem;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 2rem;
  box-shadow:
    0 8px 25px rgba(60, 199, 143, 0.3),
    0 1px 0 rgba(255, 255, 255, 0.2) inset;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.submit-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover::after {
  width: 300px;
  height: 300px;
}

.submit-button:hover {
  background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 12px 35px rgba(60, 199, 143, 0.4),
    0 1px 0 rgba(255, 255, 255, 0.3) inset;
}

.submit-button:active {
  transform: translateY(-2px) scale(1.01);
  transition: all 0.1s ease;
}

.submit-button:disabled {
  background: linear-gradient(135deg, #a5d6a7 0%, #c8e6c9 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  opacity: 0.7;
}

.submit-button:disabled::before,
.submit-button:disabled::after {
  display: none;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 0.75rem;
  border-radius: 5px;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

/* Enhanced Success Message */
.success-message {
  text-align: center;
  padding: 3rem 2rem;
  animation: successPulse 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes successPulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.success-icon {
  background: linear-gradient(135deg, #3CC78F 0%, #2e7d32 100%);
  color: white;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  margin: 0 auto 2rem;
  box-shadow:
    0 8px 25px rgba(60, 199, 143, 0.3),
    0 0 0 10px rgba(60, 199, 143, 0.1);
  animation: iconBounce 0.6s ease-out 0.3s both;
}

@keyframes iconBounce {
  0% {
    transform: scale(0);
    rotate: -180deg;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    rotate: 0deg;
  }
}

.success-message h3 {
  background: linear-gradient(135deg, #2e7d32 0%, #3CC78F 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
  font-size: 2rem;
  font-weight: 700;
}

.success-message p {
  color: #666;
  margin-bottom: 2.5rem;
  font-size: 1.2rem;
  line-height: 1.6;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Enhanced Button Group */
.button-group {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 1rem;
}

.primary-button,
.secondary-button {
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  min-width: 180px;
}

.primary-button {
  background: linear-gradient(135deg, #3CC78F 0%, #2e7d32 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 15px rgba(60, 199, 143, 0.3);
}

.primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.primary-button:hover::before {
  left: 100%;
}

.primary-button:hover {
  background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(60, 199, 143, 0.4);
}

.secondary-button {
  background: rgba(255, 255, 255, 0.9);
  color: #2e7d32;
  border: 2px solid #3CC78F;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(60, 199, 143, 0.1);
}

.secondary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(135deg, #3CC78F, #2e7d32);
  transition: width 0.4s ease;
  z-index: -1;
}

.secondary-button:hover::before {
  width: 100%;
}

.secondary-button:hover {
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(60, 199, 143, 0.3);
  border-color: #2e7d32;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .donations-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1.5rem 0.75rem;
  }

  .dashboard-nav {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-radius: 20px;
  }

  .nav-pill {
    width: 100%;
    max-width: 350px;
    justify-content: center;
    padding: 1rem 1.5rem;
  }

  .donation-form-container,
  .my-donations-container {
    padding: 2rem;
    border-radius: 20px;
  }

  .donation-form-container h2 {
    font-size: 2rem;
  }

  .location-input-group {
    flex-direction: column;
    gap: 1rem;
  }

  .button-group {
    flex-direction: column;
    gap: 1rem;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
    min-width: auto;
  }

  .my-donations-container {
    max-height: 70vh;
  }

  .donations-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .donation-image {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 1rem 0.5rem;
  }

  .dashboard-nav {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .nav-pill {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .donation-form-container,
  .my-donations-container {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .donation-form-container h2 {
    font-size: 1.75rem;
  }

  .form-group input,
  .form-group select {
    padding: 1rem;
    font-size: 1rem;
  }

  .submit-button {
    padding: 1rem;
    font-size: 1rem;
  }

  .donation-content {
    padding: 1.5rem;
  }

  .donation-title {
    font-size: 1.2rem;
  }

  .my-donations-container {
    max-height: 60vh;
  }

  .floating-element {
    font-size: 1.5rem;
  }
}

/* Additional Smooth Animations */
.form-group {
  animation: slideInForm 0.6s ease-out;
  animation-fill-mode: both;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }
.form-group:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInForm {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Smooth Hover Effects for Interactive Elements */
.donation-form-container *,
.my-donations-container * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus Visible for Accessibility */
.nav-pill:focus-visible,
.submit-button:focus-visible,
.primary-button:focus-visible,
.secondary-button:focus-visible {
  outline: 3px solid rgba(60, 199, 143, 0.5);
  outline-offset: 2px;
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 2rem 1rem;
  }
  
  .donation-form-container {
    padding: 1.5rem;
  }
  
  .donation-form-container h2 {
    font-size: 1.75rem;
  }
}

/* Add these styles to your existing Dashboard.css file */

.dashboard-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

@media (min-width: 992px) {
  .dashboard-layout {
    grid-template-columns: 1fr 1fr;
  }
}

/* Enhanced My Donations Container */
.my-donations-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(60, 199, 143, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.8) inset;
  padding: 3rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(60, 199, 143, 0.2);
  max-height: 85vh;
  overflow-y: auto;
}

.my-donations-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3CC78F 0%, #2e7d32 50%, #3CC78F 100%);
  background-size: 200% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

.my-donations-container::after {
  content: '';
  position: absolute;
  top: 6px;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(60, 199, 143, 0.02) 0%, transparent 100%);
  pointer-events: none;
}

.my-donations-container:hover {
  box-shadow:
    0 16px 48px rgba(60, 199, 143, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.9) inset;
  transform: translateY(-4px) scale(1.01);
  border-color: rgba(60, 199, 143, 0.3);
}

/* Enhanced Custom Scrollbar */
.my-donations-container::-webkit-scrollbar {
  width: 12px;
}

.my-donations-container::-webkit-scrollbar-track {
  background: rgba(60, 199, 143, 0.05);
  border-radius: 10px;
  margin: 10px 0;
}

.my-donations-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3CC78F, #2e7d32);
  border-radius: 10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 10px rgba(60, 199, 143, 0.2);
}

.my-donations-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2e7d32, #1b5e20);
  box-shadow: 0 4px 15px rgba(60, 199, 143, 0.3);
}

.my-donations-container h2 {
  color: #2e7d32;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: 600;
  position: relative;
  padding-bottom: 0.5rem;
}

.my-donations-container h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #2e7d32;
}

/* Enhanced Donations Grid */
.donations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
  padding: 1rem 0;
}

/* Modern Donation Cards */
.donation-card {
  border-radius: 20px;
  overflow: hidden;
  box-shadow:
    0 8px 25px rgba(60, 199, 143, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.8) inset;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid rgba(60, 199, 143, 0.15);
  animation: slideInCard 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;
}

.donation-card:nth-child(1) { animation-delay: 0.1s; }
.donation-card:nth-child(2) { animation-delay: 0.2s; }
.donation-card:nth-child(3) { animation-delay: 0.3s; }
.donation-card:nth-child(4) { animation-delay: 0.4s; }

.donation-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3CC78F, #2e7d32, #3CC78F);
  background-size: 200% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

.donation-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow:
    0 20px 40px rgba(60, 199, 143, 0.25),
    0 1px 0 rgba(255, 255, 255, 0.9) inset;
  border-color: rgba(60, 199, 143, 0.4);
}

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Donation Image */
.donation-image {
  height: 220px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.donation-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(60, 199, 143, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.donation-card:hover .donation-image::before {
  opacity: 1;
}

.donation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(1) saturate(1);
}

.donation-card:hover .donation-image img {
  transform: scale(1.1) rotate(1deg);
  filter: brightness(1.1) saturate(1.2);
}

.donation-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-style: italic;
  font-size: 1.1rem;
  background: linear-gradient(135deg, #f0f9f7 0%, #e8f5f0 100%);
}

/* Enhanced Donation Content */
.donation-content {
  padding: 2rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
}

.donation-title {
  font-size: 1.4rem;
  font-weight: 700;
  background: linear-gradient(135deg, #2e7d32 0%, #3CC78F 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
  line-height: 1.3;
  transition: all 0.3s ease;
}

.donation-card:hover .donation-title {
  transform: translateX(5px);
}

/* Enhanced Donation Details */
.donation-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(60, 199, 143, 0.1);
}

.donation-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  padding: 0.5rem 0;
  transition: all 0.3s ease;
}

.donation-detail:hover {
  transform: translateX(5px);
  background: rgba(60, 199, 143, 0.05);
  margin: 0 -1rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

.detail-label {
  font-weight: 600;
  color: #555;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-label::before {
  content: '•';
  color: #3CC78F;
  font-weight: bold;
}

.detail-value {
  color: #2e7d32;
  font-weight: 600;
}

/* Modern Status Badges */
.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.status-available {
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
  color: #2e7d32;
  border: 1px solid #a5d6a7;
}

.status-available:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.2);
}

.status-claimed {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  color: #f57f17;
  border: 1px solid #ffcc02;
}

.status-claimed:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(245, 127, 23, 0.2);
}

.status-picked {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  border: 1px solid #64b5f6;
}

.status-picked:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(21, 101, 192, 0.2);
}

.status-expired {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  color: #c62828;
  border: 1px solid #ef5350;
}

.status-expired:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(198, 40, 40, 0.2);
}

/* Enhanced Loading States */
.loading-spinner {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(60, 199, 143, 0.1);
  border-left: 4px solid #3CC78F;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  font-size: 1.1rem;
  font-weight: 500;
  color: #3CC78F;
}

/* Enhanced No Donations State */
.no-donations {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  border: 2px dashed rgba(60, 199, 143, 0.3);
  margin: 2rem 0;
}

.no-donations-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.7;
  animation: float 3s ease-in-out infinite;
}

.no-donations h3 {
  font-size: 1.5rem;
  color: #3CC78F;
  margin-bottom: 1rem;
  font-weight: 600;
}

.no-donations p {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Enhanced Error Message */
.error-message {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  color: #c62828;
  padding: 1.25rem 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  font-size: 1rem;
  font-weight: 500;
  border: 1px solid #ef5350;
  box-shadow: 0 4px 15px rgba(198, 40, 40, 0.1);
  animation: shakeError 0.5s ease-out;
}

@keyframes shakeError {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .donations-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
  
  .donation-image {
    height: 160px;
  }
}

@media (max-width: 480px) {
  .donations-grid {
    grid-template-columns: 1fr;
  }
  
  .my-donations-container {
    padding: 1.5rem;
  }
  
  .my-donations-container h2 {
    font-size: 1.75rem;
  }
}
/* request tab styling */


.request-tab-container {
  position: relative;
  z-index: 1100;
}

.request-tab-button {
  background-color: #3CC78F;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.request-tab-button:hover {
  background-color: #2e7d32;
  transform: scale(1.05);
}

.request-icon {
  font-size: 1.2rem;
}

.request-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
}

.request-tab-panel {
  position: absolute;
  top: 50px;
  right: 0;
  width: 320px;
  max-height: 450px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transform: translateY(-20px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.request-tab-panel.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.request-tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #3CC78F;
  color: white;
}

.request-tab-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.request-tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.request-list {
  flex: 1;
  overflow-y: auto;
  max-height: 350px;
}

.request-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.request-item:hover {
  background-color: #f9f9f9;
}

.request-item.unread {
  background-color: #e8f5e9;
}

.request-item.unread:hover {
  background-color: #d7edd8;
}

.request-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 5px;
}

.request-item-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.request-time {
  font-size: 0.75rem;
  color: #777;
  white-space: nowrap;
}

.request-from {
  margin: 5px 0;
  font-size: 0.9rem;
  color: #555;
}

.request-status {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-indicator.pending {
  background-color: #FFC107;
}

.status-indicator.accepted {
  background-color: #4CAF50;
}

.status-indicator.rejected {
  background-color: #F44336;
}

.status-text {
  font-size: 0.8rem;
  color: #666;
}

.request-actions {
  display: flex;
  gap: 8px;
}

.accept-button,
.reject-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.accept-button {
  background-color: #3CC78F;
  color: white;
}

.accept-button:hover {
  background-color: #2e7d32;
}

.reject-button {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.reject-button:hover {
  background-color: #e0e0e0;
}

.view-all-button {
  margin: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.view-all-button:hover {
  background-color: #e0e0e0;
}

.request-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: #666;
}

.request-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3CC78F;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.no-requests {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
  text-align: center;
}

.no-requests-icon {
  font-size: 2rem;
  margin-bottom: 10px;
  opacity: 0.7;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive styles */
@media (max-width: 480px) {
  .request-tab-panel {
    width: 100%;
    max-width: 100%;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    max-height: 100vh;
  }
  
  .request-list {
    max-height: calc(100vh - 130px);
  }
}
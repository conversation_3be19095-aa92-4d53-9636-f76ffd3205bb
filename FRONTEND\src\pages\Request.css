.requests-page {
  min-height: calc(100vh - 150px);
  background-color: #f9f9f9;
  padding: 3rem 1rem;
}

.requests-container {
  max-width: 1200px;
  margin: 0 auto;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  transition: all 0.3s ease;
}

.requests-container:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.requests-header {
  margin-bottom: 2rem;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 1rem;
}

.requests-header h2 {
  color: #3CC78F;
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
  text-align: center;
}

.requests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.request-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #eee;
  display: flex;
  flex-direction: column;
}

.request-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.request-card-header {
  padding: 1.25rem;
  border-bottom: 1px solid #f0f0f0;
}

.request-card-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: #333;
  font-weight: 600;
}

.request-card-subtitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.request-from {
  font-size: 0.9rem;
  color: #666;
}

.request-date {
  font-size: 0.8rem;
  color: #888;
}

.request-card-content {
  padding: 1.25rem;
  flex: 1;
}

.request-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.request-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.95rem;
}

.detail-label {
  font-weight: 500;
  color: #666;
}

.detail-value {
  color: #333;
  text-align: right;
  max-width: 60%;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.85rem;
  font-weight: 500;
  text-align: center;
}

.status-pending {
  background-color: #fff8e1;
  color: #f57f17;
}

.status-accepted {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-rejected {
  background-color: #ffebee;
  color: #c62828;
}

.request-card-footer {
  padding: 1rem 1.25rem;
  border-top: 1px solid #f0f0f0;
}

.request-actions {
  display: flex;
  gap: 0.75rem;
}

.action-button {
  padding: 0.5rem 1rem;
  border-radius: 5px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  flex: 1;
}

.accept-button {
  background-color: #3CC78F;
  color: white;
}

.accept-button:hover {
  background-color: #2e7d32;
}

.reject-button {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.reject-button:hover {
  background-color: #e0e0e0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3CC78F;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 5px;
  margin-bottom: 1.5rem;
  text-align: center;
}

.no-requests {
  text-align: center;
  padding: 3rem 1rem;
}

.no-requests-icon {
  font-size: 3rem;
  color: #ccc;
  margin-bottom: 1rem;
}

.no-requests h3 {
  color: #666;
  margin-bottom: 0.5rem;
}

.no-requests p {
  color: #888;
  max-width: 400px;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive styles */
@media (max-width: 992px) {
  .requests-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .requests-container {
    padding: 1.5rem;
  }
  
  .requests-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .requests-page {
    padding: 2rem 1rem;
  }
  
  .requests-container {
    padding: 1rem;
  }
  
  .requests-header h2 {
    font-size: 1.5rem;
  }
  
  .request-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .action-button {
    width: 100%;
  }
  
  .detail-value {
    max-width: 50%;
  }
}
import express from "express";
import { register ,login , logout_user} from "../controllers/auth.controller.js";
import { isAuthenticated } from "../middleware/auth.middleware.js";
const router = express.Router();

// .POST requests

// register
router.post("/register", register);
// login
router.post("/login", login);

// GET REQUEST to logout an user
router.get("/logout_user" , logout_user)


// WHYYY
// using middleware 
router.get("/me", isAuthenticated, (req, res) => {
  res.json({user :req.user});
});


export default router;

.counter-section {
  background-color: #f9f9f9;
  padding: 5rem 2rem;
  position: relative;
  overflow: hidden;
}

.counter-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #3CC78F 0%, #2e7d32 100%);
}

.counter-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 2rem;
}

.counter-item {
  flex: 1;
  min-width: 250px;
  text-align: center;
  padding: 2rem;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.counter-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background-color: #3CC78F;
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.5s ease;
}

.counter-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.counter-item:hover::after {
  transform: scaleX(1);
}

.counter-value {
  font-size: 3.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
  height: 5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.counter-number {
  color: #3CC78F;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.counter-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  display: inline-block;
}

.counter-label::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 40px;
  height: 2px;
  background-color: #3CC78F;
  transform: translateX(-50%);
}

.counter-loading {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(60, 199, 143, 0.2);
  border-top: 4px solid #3CC78F;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.counter-error {
  text-align: center;
  color: #e53935;
  margin-top: 2rem;
  padding: 1rem;
  background-color: #ffebee;
  border-radius: 5px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
.animate__fadeIn {
  animation-name: fadeIn;
  animation-duration: 3s;
  animation-delay: 2s;

}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive styles */
@media (max-width: 992px) {
  .counter-container {
    gap: 1.5rem;
  }
  
  .counter-item {
    padding: 1.5rem;
    min-width: 200px;
  }
  
  .counter-value {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .counter-section {
    padding: 4rem 1.5rem;
  }
  
  .counter-container {
    flex-direction: column;
    max-width: 500px;
  }
  
  .counter-item {
    width: 100%;
  }
  
  .counter-item:nth-child(1) {
    animation-delay: 0.1s;
  }
  
  .counter-item:nth-child(2) {
    animation-delay: 0.3s;
  }
  
  .counter-item:nth-child(3) {
    animation-delay: 0.5s;
  }
}

@media (max-width: 480px) {
  .counter-section {
    padding: 3rem 1rem;
  }
  
  .counter-value {
    font-size: 2.5rem;
    height: 4rem;
  }
  
  .counter-label {
    font-size: 1rem;
  }
}

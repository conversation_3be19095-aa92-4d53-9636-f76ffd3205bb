// import 
import { registerUser ,loginUser } from "../services/auth.services.js"
import { cookieOptions } from "../config/config.js"


export const register = async  (req ,res) =>{
    const {name , email ,password,role} = req.body
    console.log(name , email ,password,role)
    // registerUser is a function which is defined in services folder  it returns an object with user and token
    const user = await registerUser(name,email ,password,role)

    // using user.token because it is an object  ,  Accestoken is the name of cookie
    res.cookie('AccessToken' , user.token , cookieOptions)

    // Sending response 
    res.status(200).json({message :"User successfully registered"})
}

// Login function code  
export const login = async (req ,res) =>{
    const {email,password} = req.body
    // loginUser is a function which is defined in services folder  it returns an object with user and token
    const user = await loginUser(email ,password)
    // WHYYYYYYY
    req.user = user.user
    res.cookie('AccessToken' , user.token , cookieOptions)
    // Sending response 
    res.status(200).json({user :user, message :"User logged in"})
}


// Logout function code 
export const logout_user = async (req ,res) =>{
    // clearing cookie
    res.clearCookie('AccessToken' , cookieOptions)
    res.status(200).json({message :"User logged out"})
}
import express from "express";
import {
  requestDonation,
  donateFood,
  getMyDonations,
  getAvailableDonations,
  claimedDonations,
  getMyRequests,
  acceptRequest,
  getAllDonations
} from "../controllers/donation.controller.js";
import { isAuthenticated } from "../middleware/auth.middleware.js";
const router = express.Router();

// Donors
router.post("/donate", isAuthenticated, donateFood);
router.get("/mydonations", isAuthenticated, getMyDonations);
router.get("/all-donations" , getAllDonations)

// NGO 
//this route is for NGO requesting donation  whhyy ?? when a ngo wants to clain a donation first a request will be sent to donor
router.post("/request/:donationId", isAuthenticated, requestDonation);

//this route is for NGO claiming donation and showing all claimed donations to NGO
router.get("/claimed-donations", isAuthenticated, claimedDonations);


// ALL
// this route is for showing all available donations to all users
router.get("/available-donations", getAvailableDonations);
// this route is for showing all requests 
router.get("/myrequests", isAuthenticated, getMyRequests);
// this route is for accepting request
router.post("/accept/:id", isAuthenticated, acceptRequest);

export default router;

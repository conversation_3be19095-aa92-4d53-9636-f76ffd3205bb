{"auto_complete": {"selected_items": [["co", "cookieStr"], ["<PERSON><PERSON>", "cookieparser"], ["value", "valueEnd"], ["valu", "valueStart"], ["key", "keyStart"], ["v", "valueMightNeedDecoding"], ["is", "isQuote"], ["dec", "decoder"], ["trim", "trimForward"], ["ea", "eatSpace"], ["tr", "trimBackward"], ["val", "valueEnd"], ["boun", "boundsCheck"], ["get", "getGlobals"], ["write", "writeFile"], ["read", "readFileSync"], ["ch", "charCodeAt"], ["decode", "decodeURIComponent"], ["ke", "keyEnd"], ["VALUE", "VALUE_START"], ["parse", "parseInteger"], ["un", "unparsedValues"], ["PARALL", "parallelQueries"], ["File", "FileVersion"], ["para", "parallelQueries"], ["rej", "rejected"], ["retu", "returnThenable"], ["then", "thenResolve"], ["return", "returnValue"], ["wrap", "wrapsPrimitiveReceiver"], ["_resolve", "_resolveReject"], ["inlin", "inlineExpansion"], ["SL", "INLINE_SLICE"], ["<PERSON><PERSON><PERSON>", "hasSimpleStartExpression"], ["stat", "startExpression"], ["hassimpl", "hasSimpleStartExpression"], ["ident", "Identifier"], ["var", "varExpr"], ["In", "InlineSlice"], ["exp", "expression"], ["in", "inlineExpansion"], ["ast", "astPasses"], ["node", "nodeToString"], ["sta", "start"], ["start", "startExpression"], ["end", "endExpression"], ["st", "startExpression"], ["file", "fileName"], ["con", "convertSrc"], ["al", "allocateEveryCharacters"], ["Str", "str<PERSON><PERSON><PERSON>"], ["contain", "containsWith"], ["even", "eventTransform"], ["eve", "eventTransform"], ["_eve", "_eventInfo"], ["Event", "EventTransform"], ["event", "eventNamespace"], ["harmon", "harmony-observation"], ["obse", "observe"], ["new", "newTitle"], ["away", "awayCountChanged"], ["opt", "optOrDefault"], ["vis", "visibilityState"], ["ms", "msHidden"], ["foc", "focusedProp"], ["web", "webkitHidden"], ["visi", "_visibilityChanged"], ["visib", "_visibilityChanged"], ["count", "countChanged"], ["Away", "AwayCounter"], ["ses", "sessionKeyInput"], ["Not", "NotImplementedError"], ["pre", "prepareBeforeSend"], ["full", "fullDocAfter"], ["doc", "docAfter"], ["_doc", "_docAfter"], ["dep", "<PERSON><PERSON><PERSON>"], ["br", "browserAndPlatform"], ["reje", "rejectionReasons"], ["undefin", "undefinedThis"], ["predicate", "predicatesPrimitiveString"], ["item", "itemIsErrorType"], ["to", "toFulfillmentValue"], ["ful", "fulfillValueIfEmpty"], ["resp", "responseAndBody"], ["js", "json"], ["grunt", "gruntConfig"], ["arr", "arrSparseEmpty"], ["err", "errorObj"], ["may", "<PERSON><PERSON><PERSON><PERSON>"], ["try", "tryCatch"], ["m", "manageAccess"], ["lon", "longStackTraces"], ["re", "reduce"], ["p", "Promise"], ["prot", "prototype"], ["can", "CancellationError"], ["erro", "errorTypes"], ["ex", "exportRejectionError"], ["<PERSON><PERSON>", "RejectionError"], ["o", "oRejectionError"], ["Pro", "Promise2"], ["b", "bluebird"], ["disa", "disabledFeatures"], ["enab", "enabledFeatures"], ["gr", "gruntConfig"], ["featr", "featureLoop"], ["feat", "featureEnabled"], ["index", "indexOf"], ["option", "optionalPaths"], ["optio", "optionalPaths"], ["Some", "SomePromiseArray"], ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], ["ma", "maybeWrapAsError"], ["tryC", "tryCatch1"], ["Any", "AnyPromiseArray"], ["build", "buildBrowser"], ["ass", "assertionErrorCode"], ["asy", "asyncConvert"], ["source", "sourceCode"], ["remove", "removeAsserts"], ["sou", "sourceCode"], ["src", "srcFiles"], ["Pr", "PropertiesPromiseArray"], ["timeout", "TimeoutError"], ["global", "globals"], ["<PERSON>mi", "PromiseInspection"], ["an", "AnyPromiseArray"]]}, "buffers": [{"file": "/C/Users/<USER>/querystringparser/Gruntfile.js", "settings": {"buffer_size": 5468, "line_ending": "Unix"}}, {"file": "/C/Users/<USER>/querystringparser/src/querystringparser.js", "settings": {"buffer_size": 131, "line_ending": "Unix"}}], "build_system": "<PERSON><PERSON><PERSON>", "command_palette": {"height": 81.0, "selected_items": [["javasc", "Set Syntax: JavaScript"], ["<PERSON><PERSON><PERSON><PERSON>", "Set Syntax: JavaScript"], ["bash", "Set Syntax: <PERSON> (Bash)"], ["mark", "Set Syntax: <PERSON><PERSON>"], ["java", "Set Syntax: JavaScript"], ["markdo", "Set Syntax: <PERSON><PERSON>"], ["javas", "Set Syntax: JavaScript"], ["javascrip", "Set Syntax: JavaScript"], ["javascip", "Set Syntax: JavaScript"], ["instal", "Package Control: Install Package"], ["javascript", "Set Syntax: JavaScript"], ["install", "Package Control: Install Package"], ["syntax javas", "Set Syntax: JavaScript"], ["set syntax javasci", "Set Syntax: JavaScript"], ["syntax javasc", "Set Syntax: JavaScript"], ["syntax java", "Set Syntax: JavaScript"], ["set syntax java", "Set Syntax: JavaScript"], ["set syntax javasc", "Set Syntax: JavaScript"], ["set java", "Set Syntax: JavaScript"], ["set syntax mar", "Set Syntax: <PERSON><PERSON>"], ["disable", "Package Control: Disable Package"], ["packa", "Package Control: Disable Package"], ["set synt", "Set Syntax: YAML"], ["set javasc", "Set Syntax: JavaScript"], ["g", "<PERSON><PERSON><PERSON>"], ["grunt", "<PERSON><PERSON><PERSON>"], ["pack", "Package Control: Install Package"]], "width": 400.0}, "console": {"height": 125.0}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "file_history": ["/C/Users/<USER>/querystringparser/README.md", "/C/Users/<USER>/cookieparser/src/cookieparser.js", "/C/Users/<USER>/cookieparser/src/constants.js", "/C/Users/<USER>/cookieparser/test/parse.js", "/C/Users/<USER>/cookieparser/benchmark/run.js", "/C/Users/<USER>/cookieparser/README.md", "/C/Users/<USER>/querystringparser/.gitignore", "/C/Users/<USER>/querystringparser/.npmignore", "/C/Users/<USER>/querystringparser/querystringparser.sublime-project", "/C/Users/<USER>/cookieparser/package.json", "/C/Users/<USER>/cookieparser/benchmark/wtf.txt", "/C/Users/<USER>/v8/process.txt", "/C/Users/<USER>/cookieparser/benchmark/bmark.js", "/C/Users/<USER>/bluebird/README.md", "/C/Users/<USER>/cookieparser/benchmark/stats.md", "/C/Users/<USER>/bluebird/benchmark/performance.js", "/C/Users/<USER>/cookieparser/benchmark/throwaway.js", "/C/Users/<USER>/cookieparser/benchmark/spion.js", "/C/Users/<USER>/cookieparser/js/cookieparser.js", "/C/Users/<USER>/cookieparser/cookieparser.sublime-project", "/C/Users/<USER>/cookieparser/.gitignore", "/C/Users/<USER>/cookieparser/.npmignore", "/C/Users/<USER>/cookieparser/.travis.yml", "/C/Users/<USER>/cookieparser/Gruntfile.js", "/C/Users/<USER>/cookieparser/ast_passes.js", "/C/Users/<USER>/bluebird/Gruntfile.js", "/C/Users/<USER>/cookieparser/test/test.js", "/C/Users/<USER>/cookieparser/mocharun.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/madeup-parallel/promises-bluebird.js", "/C/Users/<USER>/bluebird/benchmark/stats/latest.md", "/C/Users/<USER>/bluebird/.npmignore", "/C/Users/<USER>/Desktop/cookie_parser.js", "/C/Users/<USER>/Desktop/task.js", "/C/Users/<USER>/v8code/freeze.js", "/C/Users/<USER>/v8code/o.asm", "/C/Users/<USER>/v8/src/typedarray.js", "/C/Users/<USER>/v8/src/arraybuffer.js", "/C/Users/<USER>/bluebird/benchmark/package.json", "/C/Users/<USER>/bluebird/benchmark/doxbee-sequential/callbacks-creationix-step.js", "/C/Users/<USER>/bluebird/benchmark/madeup-parallel/callbacks-caolan-async-parallel.js", "/C/Users/<USER>/bluebird/bench", "/C/Users/<USER>/bluebird/benchmark/async-compare/madeup-parallel/callbacks-caolan-async-parallel.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/madeup-parallel/promises-calvinmetcalf-liar.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/madeup-parallel/promises-cujojs-when.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/madeup-parallel/promises-krisko<PERSON>-q.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/madeup-parallel/promises-medikoo-deferred.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/madeup-parallel/promises-obvious-kew.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/madeup-parallel/promises-tildeio-rsvp.js", "/C/Users/<USER>/bluebird/src/direct_resolve.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/madeup-parallel/promises-bluebird-generator.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/madeup-parallel/callbacks-baseline.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/performance.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/package.json", "/C/Users/<USER>/bluebird/benchmark/async-compare/doxbee-sequential/promises-bluebird-generator.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/doxbee-sequential/promises-bluebird.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/.gitignore", "/C/Users/<USER>/bluebird/test/mocha/method.js", "/C/Users/<USER>/bluebird/test/mocha/helpers/testThreeCases.js", "/C/Users/<USER>/bluebird/test/mocha/generator.js", "/C/Users/<USER>/bluebird/test/mocha/direct_resolving.js", "/C/Users/<USER>/bluebird/API.md", "/C/Users/<USER>/bluebird/changelog.md", "/C/Users/<USER>/bluebird/test/mocha/cycles.js", "/C/Users/<USER>/bluebird/src/promise.js", "/C/Users/<USER>/bluebird/src/promise_resolver.js", "/C/Users/<USER>/bluebird/src/util.js", "/C/Users/<USER>/bluebird/src/constants.js", "/C/Users/<USER>/bluebird/src/schedule.js", "/C/Users/<USER>/perf-promises/lib/runners.js", "/C/Users/<USER>/perf-promises/promises.perf.js", "/C/Users/<USER>/bluebird/test/mocha/try.js", "/C/Users/<USER>/bluebird/test/mocha/q_nodeify.js", "/C/Users/<USER>/bluebird/benchmark/stats/latest-spion-async-compare.md", "/C/Users/<USER>/bluebird/benchmark/async-compare/parallel/callbacks-caolan-async-parallel.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/lib/fakesP.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/lib/dummy.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/lib/fakemaker.js", "/C/Users/<USER>/bluebird/benchmark/stats/dev-19102013.md", "/C/Users/<USER>/bluebird/src/value_resolving.js", "/C/Users/<USER>/bluebird/src/promise_spawn.js", "/C/Server/public_html/petka/discountcode/discount_code_generator.php", "/C/Server/public_html/petka/discountcode/20_jani_<PERSON><PERSON>_15112013.sql", "/C/Server/public_html/petka/discountcode/20_jani_<PERSON><PERSON>_15112013.txt", "/C/Server/public_html/petka/discountcode/30_popeda_15112013.sql", "/C/Server/public_html/petka/discountcode/30_popeda_15112013.txt", "/C/Users/<USER>/node-toobusy/toobusy.js", "/C/Users/<USER>/bluebird/.gitignore", "/C/Users/<USER>/bluebird/src/generators.js", "/C/Users/<USER>/bluebird/node_modules/grunt/lib/util/task.js", "/C/Users/<USER>/bluebird/browser/mocha.js", "/C/Users/<USER>/bluebird/throwaway.js", "/C/Users/<USER>/bluebird/node_modules/mocha/mocha.js", "/C/Users/<USER>/promises-tests/lib/tests/helpers/thenables.js", "/C/Users/<USER>/bluebird/test/mocha/helpers/thenables.js", "/C/Users/<USER>/bluebird/bundle.js", "/C/Users/<USER>/bluebird/test/mocha/promisify.js", "/C/Users/<USER>/AppData/Local/Temp/scp41918/var/www/nodejs/chat/server_error.log", "/C/Users/<USER>/AppData/Local/Temp/scp41501/var/www/nodejs/chat/server.log", "/C/Users/<USER>/AppData/Local/Temp/scp41553/var/www/nodejs/chat/single_emitter.js", "/C/Users/<USER>/bluebird/test/mocha/q_inspect.js", "/C/Users/<USER>/livemusicstage/static/js/widgets/chat/away_counter.js", "/C/Users/<USER>/Desktop/guide_part1.md", "/C/Users/<USER>/v8/src/runtime.cc", "/C/Users/<USER>/v8/src/heap.cc", "/C/Users/<USER>/bluebird/ast_passes.js", "/C/Users/<USER>/bluebird/src/call_get.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/node_modules/bluebird/js/main/simple_thenables.js", "/C/Users/<USER>/bluebird/test/mocha/q_spread.js", "/C/Users/<USER>/bluebird/npm-debug.log", "/C/Server/public_html/petka/msnfuck/lol.js", "/C/Server/public_html/petka/msnfuck/msnfuck.js", "/C/Users/<USER>/bluebird/js/zalgo/call_get.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/node_modules/bluebird/js/main/call_get.js", "/C/Users/<USER>/bluebird/js/main/call_get.js", "/C/Users/<USER>/input.js", "/C/Users/<USER>/test.js", "/C/Users/<USER>/livemusicstage/static/js/widgets/chat/chat_widget.js", "/C/Users/<USER>/livemusicstage/static/js/widgets/chat/chat_client.js", "/C/Users/<USER>/bluebird/browser/mocha_init.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/dev-bluebird/promises-tildeio-rsvp.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/dev-bluebird/promises-calvinmetcalf-liar.js", "/C/Users/<USER>/livemusicstage/application/config/js_packages.php", "/C/Users/<USER>/livemusicstage/static/js/lib/observable/observable.js", "/C/Users/<USER>/bluebird/benchmark/async-compare/dev-bluebird/promises-medikoo-deferred.js", "/C/Users/<USER>/asd2.js", "/C/Users/<USER>/bluebird/test/mocha/q_done.js", "/C/Users/<USER>/livemusicstage/chat/package.json", "/C/Users/<USER>/livemusicstage/chat/init.js"], "find": {"height": 34.0}, "find_in_files": {"height": 0.0, "where_history": []}, "find_state": {"case_sensitive": true, "find_history": ["cook", "cookieparser", "bluebird", "'", "googleCookie", "586", "test(", "needsDecoding", "buffer", "callback", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@", "── ", "returnValue", "test", "tryAssume", "statsTemplate", "SLICE_INLINE", "Start", "start", "1,1,1,1,1,1,1,1,1,1,1,1,1", ",", "1,1,1,1,1,1,1,1,1,1,1,1,1", ",", ", ", "a", "a, b, c, d, e, f, g, h, i, j, k", "Workaround", "getOptStatus", "next", "EventStream", "<div>", "</div>", "destroy", "messageReceived", ".then", "~[^\"]+", "get", "ignoreleaks", "checkglobals", "#stats", "stats", "#stats", "stats", "obj", "browserName:", "watch", "saving and abusing", "console", "Promise.onPossiblyUnhandledRejection", "unhandledRej", "console", "see", ".race", "Promise.race", "1", "fulfill", "Any", "any", "AnyPromiseArray", "\"try", "cc", "console.", "console", "applyOptionalRequires", "PropertiesPromise", "this._bitField = 67108864", "function Promise( resolver )", "var longStackTraces =", "function Promise( resolver )", "function Promise", "have long", "longStackTraces =", "isSupported", "longStackTraces =", "`([^`]+)`", "\\[([^\\]]+)\\]\\(([^\\)]+)\\)", "`([a-zA-Z0-9_-]+)`", ", `", "setTrace", "captureStackTrace", "Error.captureStackTrace", "should wrap", "back", "stringback", "new Promise.RejectionError", "new Error", "console", "util", "inherits", "optionalModuleRequireMap", "BUILD_DEBUG_DEST", "inspect", "toJSON", "Thenable", "thena", "applyMutExPaths", "toLowerCase()", "getBrowserBuildHeader", "browserBuild", "getOptionalRequireCode", "isPromise", "Promise._cast", "cast(", "Thenable", "thenable", "tryThenab", "nodeback", "isPromise", "filter", "isMinified", "license", "Promise$_All", "astPasses.", "Promise$_Deferred", "deferFn", "promisetryfunc", "val", "false", "true", "tryCatch", "\"", "asd2", "ret", "throwaway.js", " Promise$_All", "Promise$_Map", "nodebackForResolver"], "highlight": true, "in_selection": false, "preserve_case": false, "regex": true, "replace_history": ["\"", "\": \"~", "", "throwValue", "End", "end", ", ", "\\n", "latest", "3", "2", "reject", "<code>$1</code>", "<a href=\"$2\">$1</a>", "<code>$1</code>", "</td><td>`", "should not wrap", "throw", "typeback", "errback", "tryCatchers[", "", "some", "count", "value", "fulfillmentValues"], "reverse": false, "show_context": true, "use_buffer2": true, "whole_word": false, "wrap": true}, "groups": [{"selected": 1, "sheets": [{"buffer": 0, "file": "/C/Users/<USER>/querystringparser/Gruntfile.js", "settings": {"buffer_size": 5468, "regions": {}, "selection": [[3081, 3067]], "settings": {"syntax": "Packages/JavaScript/JavaScript.tmLanguage", "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 1434.0, "zoom_level": 1.0}, "type": "text"}, {"buffer": 1, "file": "/C/Users/<USER>/querystringparser/src/querystringparser.js", "settings": {"buffer_size": 131, "regions": {}, "selection": [[128, 128]], "settings": {"syntax": "Packages/JavaScript/JavaScript.tmLanguage", "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "type": "text"}]}, {"sheets": []}], "incremental_find": {"height": 0.0}, "input": {"height": 31.0}, "layout": {"cells": [[0, 0, 1, 1], [1, 0, 2, 1]], "cols": [0.0, 0.*********, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.exec": {"height": 122.0}, "replace": {"height": 62.0}, "save_all_on_build": false, "select_file": {"height": 0.0, "selected_items": [["sch", "src/schedule.js"], ["packa", "package.json"], ["mochapromis", "test/mocha/promisify.js"], ["promise.js", "src/promise.js"], ["m<PERSON><PERSON><PERSON><PERSON>", "test/mocha/unhandled_rejections.js"], ["race_prom", "src/race_promise_array.js"], ["when_any", "test/mocha/when_any.js"], ["mochabind", "test/mocha/bind.js"], ["sparsit", "test/mocha/sparsity.js"], ["genera", "src/generators.js"], ["generat", "src/generators.js"], ["throwawa", "throwaway.js"], ["changel", "changelog.md"], ["hangelog", "changelog.js"], ["todo", "todo.txt"], ["bench", "bench"], ["grunt", "Gruntfile.js"], ["async-compare/examples", "benchmark/async-compare/node_modules/long-stack-traces/examples.html"], ["bind.js", "test/mocha/bind.js"], ["debug-env", "test/mocha/bluebird-debug-env-flag.js"], ["promise", "src/promise.js"], ["async_compare", "benchmark/async-compare/node_modules/stratifiedjs/modules/compare.sjs"], ["async.js", "src/async.js"], ["async", "async"]], "width": 0.0}, "select_project": {"height": 0.0, "selected_items": [], "width": 0.0}, "show_minimap": false, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 384.0, "status_bar_visible": true}
import User from "../models/user.model.js";

export const findUserByEmail = async (email) => {
  const user = await User.findOne({ email });
  return user;
};
export const findUserByEmailandPassword = async (email) => {
  const user = await User.findOne( {email} ).select("+password"); // select password is used to select password from database
  return user;
}
// create user  in database     
export const createUser = async (name, email, password,role) => { // role is default from code
  const user = new User({
    name,
    email,
    password,
    role, // default from code
    location: {
      type: "Point",
      coordinates: [77.5946, 12.9716], // Bangalore default
    },
  });
  await user.save();
  return user;
};

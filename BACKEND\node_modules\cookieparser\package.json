{"name": "cookieparser", "description": "cookieparser", "version": "0.1.0", "keywords": ["cookie", "parse", "parser", "serialize", "serializer", "cookies", "key-value"], "scripts": {"test": "grunt test"}, "homepage": "https://github.com/petkaantonov/cookieparser", "repository": {"type": "git", "url": "git://github.com/petkaantonov/cookieparser.git"}, "bugs": {"url": "http://github.com/petkaantonov/cookieparser/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://github.com/petkaantonov/"}, "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-jshint": "~0.6.4", "jshint-stylish": "latest", "acorn": "~0.3.1", "grunt-bump": "0.0.11", "mocha": "~1.12.1", "grunt-cli": "~0.1.9", "cookie": "latest", "benchmark": "latest", "expect.js": "latest"}, "readmeFilename": "README.md", "main": "./js/cookieparser.js"}